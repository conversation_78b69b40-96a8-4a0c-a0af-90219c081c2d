# Kafka 4.0 KRaft 模式集群部署指南

## 概述

本指南提供了在三台服务器上部署 Kafka 4.0 KRaft 模式集群的完整方案。

### 环境信息
- **Kafka 版本**: 4.0
- **模式**: KRaft (无需 ZooKeeper)
- **节点**: ***************-103
- **部署路径**: /export/server/kafka
- **数据目录**: /export/data/kafka
- **日志目录**: /export/logs/kafka
- **认证**: SASL/PLAIN

## 目录结构

```
kafka-config/
├── node-101/
│   └── server.properties          # 节点101配置
├── node-102/
│   └── server.properties          # 节点102配置
├── node-103/
│   └── server.properties          # 节点103配置
└── common/
    ├── log4j.properties           # 日志配置
    ├── kafka_server_jaas.conf     # SASL认证配置
    ├── client.properties          # 客户端配置
    ├── producer.properties        # 生产者配置
    └── consumer.properties        # 消费者配置

kafka-scripts/
├── 01-init-kafka-cluster.sh       # 集群初始化脚本
├── 02-start-kafka-cluster.sh      # 集群启动脚本
├── 03-stop-kafka-cluster.sh       # 集群停止脚本
├── 04-check-kafka-cluster.sh      # 集群状态检查脚本
└── 05-test-kafka-cluster.sh       # 集群测试脚本
```

## 部署步骤

### 1. 环境准备

在每台服务器上执行：

```bash
# 创建用户和组
groupadd kafka
useradd -g kafka kafka

# 创建目录
mkdir -p /export/server
mkdir -p /export/data/kafka
mkdir -p /export/logs/kafka

# 设置权限
chown -R kafka:kafka /export/data/kafka
chown -R kafka:kafka /export/logs/kafka
```

### 2. 安装 Kafka 4.0

```bash
# 下载 Kafka 4.0
cd /export/server
wget https://downloads.apache.org/kafka/2.13-4.0.0/kafka_2.13-4.0.0.tgz
tar -xzf kafka_2.13-4.0.0.tgz
ln -s kafka_2.13-4.0.0 kafka

# 设置权限
chown -R kafka:kafka /export/server/kafka*
```

### 3. 配置防火墙

```bash
# 开放端口
firewall-cmd --permanent --add-port=9092/tcp  # Broker端口
firewall-cmd --permanent --add-port=9093/tcp  # Controller端口
firewall-cmd --permanent --add-port=9999/tcp  # JMX端口
firewall-cmd --reload
```

### 4. 系统优化

```bash
# 修改 /etc/security/limits.conf
echo "kafka soft nofile 65536" >> /etc/security/limits.conf
echo "kafka hard nofile 65536" >> /etc/security/limits.conf
echo "kafka soft nproc 32768" >> /etc/security/limits.conf
echo "kafka hard nproc 32768" >> /etc/security/limits.conf

# 修改内核参数
echo 'vm.swappiness=1' >> /etc/sysctl.conf
echo 'vm.dirty_background_ratio=5' >> /etc/sysctl.conf
echo 'vm.dirty_ratio=10' >> /etc/sysctl.conf
sysctl -p
```

### 5. 部署配置文件

```bash
# 使脚本可执行
chmod +x kafka-scripts/*.sh

# 运行初始化脚本
./kafka-scripts/01-init-kafka-cluster.sh
```

### 6. 启动集群

```bash
# 启动集群
./kafka-scripts/02-start-kafka-cluster.sh

# 检查集群状态
./kafka-scripts/04-check-kafka-cluster.sh
```

### 7. 测试集群

```bash
# 运行基本测试
./kafka-scripts/05-test-kafka-cluster.sh basic

# 运行完整测试
./kafka-scripts/05-test-kafka-cluster.sh all
```

## 重要配置说明

### KRaft 模式关键配置

1. **process.roles**: 设置为 `controller,broker` (组合模式)
2. **node.id**: 每个节点唯一的ID (1, 2, 3)
3. **controller.quorum.voters**: 控制器仲裁配置
4. **listeners**: 包含 SASL_PLAINTEXT 和 CONTROLLER 监听器

### SASL 认证配置

- **用户账号**:
  - kafka/kafka-secret (服务间通信)
  - admin/admin-secret (管理员)
  - producer/producer-secret (生产者)
  - consumer/consumer-secret (消费者)

### 端口说明

- **9092**: Broker 端口 (SASL_PLAINTEXT)
- **9093**: Controller 端口 (PLAINTEXT)
- **9999**: JMX 监控端口

## 管理命令

### 启动/停止集群

```bash
# 启动集群
./kafka-scripts/02-start-kafka-cluster.sh

# 停止集群
./kafka-scripts/03-stop-kafka-cluster.sh

# 检查状态
./kafka-scripts/04-check-kafka-cluster.sh
```

### Topic 管理

```bash
# 创建 Topic
bin/kafka-topics.sh --create --topic my-topic \
  --bootstrap-server ***************:9092 \
  --command-config config/client.properties \
  --partitions 3 --replication-factor 3

# 列出 Topic
bin/kafka-topics.sh --list \
  --bootstrap-server ***************:9092 \
  --command-config config/client.properties

# 查看 Topic 详情
bin/kafka-topics.sh --describe --topic my-topic \
  --bootstrap-server ***************:9092 \
  --command-config config/client.properties
```

### 生产和消费消息

```bash
# 生产消息
bin/kafka-console-producer.sh \
  --bootstrap-server ***************:9092 \
  --producer.config config/producer.properties \
  --topic my-topic

# 消费消息
bin/kafka-console-consumer.sh \
  --bootstrap-server ***************:9092 \
  --consumer.config config/consumer.properties \
  --topic my-topic --from-beginning
```

## 监控和维护

### JMX 监控

可以使用 JConsole 或其他 JMX 工具连接到：
- ***************:9999
- 192.168.200.102:9999
- 192.168.200.103:9999

### 日志文件位置

- **服务器日志**: /export/logs/kafka/server.log
- **控制器日志**: /export/logs/kafka/controller.log
- **状态变更日志**: /export/logs/kafka/state-change.log
- **请求日志**: /export/logs/kafka/kafka-request.log

### 常见问题排查

1. **集群无法启动**:
   - 检查端口是否被占用
   - 检查 SASL 配置是否正确
   - 查看服务器日志

2. **认证失败**:
   - 检查 JAAS 配置文件
   - 验证用户名密码
   - 确认客户端配置

3. **性能问题**:
   - 检查磁盘 I/O
   - 监控网络连接
   - 调整 JVM 参数

## 安全建议

1. **更改默认密码**: 修改 JAAS 配置中的默认密码
2. **网络隔离**: 使用防火墙限制访问
3. **SSL/TLS**: 生产环境建议启用 SSL
4. **定期备份**: 备份重要的配置和数据

## 升级和维护

1. **滚动升级**: 逐个节点升级，保持服务可用
2. **配置变更**: 使用动态配置更新功能
3. **日志清理**: 定期清理旧日志文件
4. **监控告警**: 设置关键指标监控

---

**注意**: 这是 Kafka 4.0 KRaft 模式的部署方案，不再需要 ZooKeeper。如果需要使用现有的 ZooKeeper 集群，请考虑使用 Kafka 3.8.x 版本。
