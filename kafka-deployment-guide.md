# Kafka 4.0 KRaft 模式集群部署指南

## 概述

本指南提供了在三台服务器上部署 Kafka 4.0 KRaft 模式集群的完整方案。

### 环境信息
- **Kafka 版本**: 4.0
- **模式**: KRaft (无需 ZooKeeper)
- **节点**: ***************-103
- **部署路径**: /export/server/kafka
- **数据目录**: /export/data/kafka
- **日志目录**: /export/logs/kafka
- **认证**: SASL/PLAIN

## 目录结构

```
kafka-config/
├── node-101/
│   └── server.properties          # 节点101配置
├── node-102/
│   └── server.properties          # 节点102配置
├── node-103/
│   └── server.properties          # 节点103配置
└── common/
    ├── log4j.properties           # 日志配置
    ├── kafka_server_jaas.conf     # SASL认证配置
    ├── client.properties          # 客户端配置
    ├── producer.properties        # 生产者配置
    └── consumer.properties        # 消费者配置

kafka-scripts/
├── 01-init-kafka-cluster.sh       # 集群初始化脚本
├── 02-start-kafka-cluster.sh      # 集群启动脚本
├── 03-stop-kafka-cluster.sh       # 集群停止脚本
├── 04-check-kafka-cluster.sh      # 集群状态检查脚本
└── 05-test-kafka-cluster.sh       # 集群测试脚本
```

## 部署步骤

### 1. 环境准备

在每台服务器上执行：

```bash
# 创建用户和组
groupadd kafka
useradd -g kafka kafka

# 创建目录
mkdir -p /export/server
mkdir -p /export/data/kafka
mkdir -p /export/logs/kafka

# 设置权限
chown -R kafka:kafka /export/data/kafka
chown -R kafka:kafka /export/logs/kafka
```

### 2. 安装 Kafka 4.0

```bash
# 下载 Kafka 4.0
cd /export/server
wget https://downloads.apache.org/kafka/2.13-3.8.0/kafka_2.13-3.8.0.tgz
tar -xzf kafka_2.13-3.8.0.tgz
ln -s kafka_2.13-3.8.0 kafka

# 设置权限
chown -R kafka:kafka /export/server/kafka*
```

### 3. 配置文件部署

```bash
# 复制配置文件到各节点
# 节点101
scp kafka-config/node-101/server.properties root@***************:/export/server/kafka/config/
scp kafka-config/common/* root@***************:/export/server/kafka/config/

# 节点102
scp kafka-config/node-102/server.properties root@***************:/export/server/kafka/config/
scp kafka-config/common/* root@***************:/export/server/kafka/config/

# 节点103
scp kafka-config/node-103/server.properties root@***************:/export/server/kafka/config/
scp kafka-config/common/* root@***************:/export/server/kafka/config/
```

### 4. 集群初始化

```bash
# 使用初始化脚本
chmod +x kafka-scripts/*.sh
./kafka-scripts/01-init-kafka-cluster.sh
```

### 5. 启动集群

```bash
# 启动集群
./kafka-scripts/02-start-kafka-cluster.sh
```

### 6. 验证集群

```bash
# 检查集群状态
./kafka-scripts/04-check-kafka-cluster.sh

# 运行测试
./kafka-scripts/05-test-kafka-cluster.sh
```

## 重要配置说明

### KRaft 模式关键配置

```properties
# 启用 KRaft 模式
process.roles=controller,broker

# 节点ID (每个节点不同)
node.id=1

# Controller 仲裁配置
controller.quorum.voters=1@***************:9093,2@***************:9093,3@***************:9093

# 监听配置
listeners=SASL_PLAINTEXT://***************:9092,CONTROLLER://***************:9093
controller.listener.names=CONTROLLER
```

### SASL 认证配置

```properties
# SASL 配置
security.inter.broker.protocol=SASL_PLAINTEXT
sasl.mechanism.inter.broker.protocol=PLAIN
sasl.enabled.mechanisms=PLAIN
```

### 用户账号

- **kafka**: kafka-secret (服务间通信)
- **admin**: admin-secret (管理员)
- **producer**: producer-secret (生产者)
- **consumer**: consumer-secret (消费者)

## 管理命令

### 启动/停止集群

```bash
# 启动集群
./kafka-scripts/02-start-kafka-cluster.sh

# 停止集群
./kafka-scripts/03-stop-kafka-cluster.sh

# 检查状态
./kafka-scripts/04-check-kafka-cluster.sh
```

### Topic 管理

```bash
# 创建 Topic
bin/kafka-topics.sh --create --topic my-topic \
  --bootstrap-server ***************:9092 \
  --command-config config/client.properties \
  --partitions 3 --replication-factor 3

# 列出 Topic
bin/kafka-topics.sh --list \
  --bootstrap-server ***************:9092 \
  --command-config config/client.properties

# 查看 Topic 详情
bin/kafka-topics.sh --describe --topic my-topic \
  --bootstrap-server ***************:9092 \
  --command-config config/client.properties
```

### 生产和消费消息

```bash
# 生产消息
bin/kafka-console-producer.sh \
  --bootstrap-server ***************:9092 \
  --producer.config config/producer.properties \
  --topic my-topic

# 消费消息
bin/kafka-console-consumer.sh \
  --bootstrap-server ***************:9092 \
  --consumer.config config/consumer.properties \
  --topic my-topic --from-beginning
```

## 监控和维护

### JMX 监控

每个节点的 JMX 端口：
- ***************:9999
- ***************:9999
- ***************:9999

### 日志文件

- **服务日志**: /export/logs/kafka/server.log
- **控制器日志**: /export/logs/kafka/controller.log
- **状态变更日志**: /export/logs/kafka/state-change.log
- **请求日志**: /export/logs/kafka/kafka-request.log

### 性能调优

```bash
# 系统参数优化
echo 'vm.swappiness=1' >> /etc/sysctl.conf
echo 'vm.dirty_background_ratio=5' >> /etc/sysctl.conf
echo 'vm.dirty_ratio=10' >> /etc/sysctl.conf
sysctl -p

# 文件句柄限制
echo 'kafka soft nofile 65536' >> /etc/security/limits.conf
echo 'kafka hard nofile 65536' >> /etc/security/limits.conf
```

## 故障排除

### 常见问题

1. **集群无法启动**
   - 检查配置文件语法
   - 确认端口未被占用
   - 检查 SASL 认证配置

2. **节点无法加入集群**
   - 检查网络连通性
   - 确认 controller.quorum.voters 配置正确
   - 检查集群 ID 是否一致

3. **认证失败**
   - 检查 JAAS 配置文件
   - 确认用户名密码正确
   - 检查 KAFKA_OPTS 环境变量

### 日志分析

```bash
# 查看启动日志
tail -f /export/logs/kafka/server.log

# 查看错误日志
grep -i error /export/logs/kafka/server.log

# 查看控制器日志
tail -f /export/logs/kafka/controller.log
```

## 安全建议

1. **修改默认密码**: 更改 JAAS 配置中的默认密码
2. **网络隔离**: 使用防火墙限制访问
3. **SSL/TLS**: 生产环境建议启用 SSL
4. **ACL**: 配置访问控制列表
5. **定期备份**: 备份配置文件和重要数据

## 升级和维护

### 滚动升级

1. 逐个停止节点
2. 更新 Kafka 版本
3. 更新配置文件
4. 重启节点
5. 验证集群状态

### 数据备份

```bash
# 备份配置文件
tar -czf kafka-config-backup-$(date +%Y%m%d).tar.gz /export/server/kafka/config/

# 备份数据目录（建议使用快照）
rsync -av /export/data/kafka/ /backup/kafka-data/
```

这个部署方案提供了完整的 Kafka 4.0 KRaft 模式集群部署和管理解决方案。
