#!/bin/bash

# Kafka 4.0 KRaft Cluster Start Script
# This script starts the Kafka cluster in KRaft mode

set -e

# Configuration
KAFKA_HOME="/export/server/kafka"
LOG_DIR="/export/logs/kafka"
SERVERS=("***************" "***************" "***************")

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Kafka is already running
check_kafka_running() {
    local server=$1
    local result=$(ssh root@$server "ps aux | grep kafka | grep -v grep | wc -l")
    return $result
}

# Function to start Kafka on a server
start_kafka_node() {
    local server=$1
    local node_id=$2
    
    print_info "Starting Kafka on $server (node-$node_id)..."
    
    # Check if already running
    if check_kafka_running $server; then
        print_warn "Kafka is already running on $server"
        return 0
    fi
    
    # Start Kafka
    ssh root@$server "
        export KAFKA_OPTS='-Djava.security.auth.login.config=$KAFKA_HOME/config/kafka_server_jaas.conf -Xmx2G -Xms2G'
        cd $KAFKA_HOME
        nohup bin/kafka-server-start.sh config/server.properties > $LOG_DIR/kafka.out 2>&1 &
        echo \$! > $LOG_DIR/kafka.pid
    "
    
    # Wait a moment for startup
    sleep 5
    
    # Verify startup
    if check_kafka_running $server; then
        print_info "Kafka started successfully on $server"
    else
        print_error "Failed to start Kafka on $server"
        return 1
    fi
}

# Function to wait for cluster to be ready
wait_for_cluster() {
    print_info "Waiting for cluster to be ready..."
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        print_info "Checking cluster status (attempt $attempt/$max_attempts)..."
        
        # Try to get broker list
        if $KAFKA_HOME/bin/kafka-broker-api-versions.sh \
            --bootstrap-server ***************:9092 \
            --command-config $KAFKA_HOME/config/client.properties \
            >/dev/null 2>&1; then
            print_info "Cluster is ready!"
            return 0
        fi
        
        sleep 10
        ((attempt++))
    done
    
    print_error "Cluster failed to become ready within expected time"
    return 1
}

# Function to show cluster status
show_cluster_status() {
    print_info "Checking cluster status..."
    
    for i in "${!SERVERS[@]}"; do
        server=${SERVERS[$i]}
        node_id=$((i + 1))
        
        echo "=== Node $node_id ($server) ==="
        
        # Check process
        local process_count=$(ssh root@$server "ps aux | grep kafka | grep -v grep | wc -l")
        if [ $process_count -gt 0 ]; then
            echo "✓ Kafka process: Running"
        else
            echo "✗ Kafka process: Not running"
        fi
        
        # Check port
        local port_status=$(ssh root@$server "netstat -tlnp | grep :9092 | wc -l")
        if [ $port_status -gt 0 ]; then
            echo "✓ Port 9092: Listening"
        else
            echo "✗ Port 9092: Not listening"
        fi
        
        # Check controller port
        local controller_port=$(ssh root@$server "netstat -tlnp | grep :9093 | wc -l")
        if [ $controller_port -gt 0 ]; then
            echo "✓ Controller port 9093: Listening"
        else
            echo "✗ Controller port 9093: Not listening"
        fi
        
        echo ""
    done
    
    # Show broker list
    print_info "Broker list:"
    $KAFKA_HOME/bin/kafka-broker-api-versions.sh \
        --bootstrap-server ***************:9092 \
        --command-config $KAFKA_HOME/config/client.properties 2>/dev/null | head -5
}

# Main function
main() {
    print_info "Starting Kafka 4.0 KRaft cluster..."
    
    # Check if Kafka is installed
    if [ ! -d "$KAFKA_HOME" ]; then
        print_error "Kafka not found at $KAFKA_HOME"
        exit 1
    fi
    
    # Start each node
    for i in "${!SERVERS[@]}"; do
        server=${SERVERS[$i]}
        node_id=$((i + 1))
        
        start_kafka_node $server $node_id
        
        # Wait between node starts
        if [ $i -lt $((${#SERVERS[@]} - 1)) ]; then
            sleep 10
        fi
    done
    
    # Wait for cluster to be ready
    wait_for_cluster
    
    # Show cluster status
    show_cluster_status
    
    print_info "Kafka 4.0 KRaft cluster started successfully!"
    print_info "You can now create topics and start producing/consuming messages."
    print_info "Use './04-check-kafka-cluster.sh' to monitor the cluster status."
}

# Run main function
main "$@"
