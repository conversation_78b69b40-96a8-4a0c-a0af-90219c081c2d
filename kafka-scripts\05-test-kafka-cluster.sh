#!/bin/bash

# Kafka 4.0 KRaft Cluster Test Script
# This script tests the Kafka cluster functionality

set -e

# Configuration
KAFKA_HOME="/export/server/kafka"
TEST_TOPIC="test-topic-$(date +%s)"
BOOTSTRAP_SERVERS="***************:9092,***************:9092,***************:9092"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Function to create test topic
create_test_topic() {
    print_header "Creating Test Topic"
    
    print_info "Creating topic: $TEST_TOPIC"
    
    $KAFKA_HOME/bin/kafka-topics.sh \
        --create \
        --topic $TEST_TOPIC \
        --bootstrap-server $BOOTSTRAP_SERVERS \
        --command-config $KAFKA_HOME/config/client.properties \
        --partitions 3 \
        --replication-factor 3
    
    if [ $? -eq 0 ]; then
        print_info "Topic created successfully"
    else
        print_error "Failed to create topic"
        return 1
    fi
    
    # Describe the topic
    print_info "Topic details:"
    $KAFKA_HOME/bin/kafka-topics.sh \
        --describe \
        --topic $TEST_TOPIC \
        --bootstrap-server $BOOTSTRAP_SERVERS \
        --command-config $KAFKA_HOME/config/client.properties
    
    echo ""
}

# Function to test producer
test_producer() {
    print_header "Testing Producer"
    
    print_info "Sending test messages to topic: $TEST_TOPIC"
    
    # Create test messages
    local test_messages=(
        "Test message 1 - $(date)"
        "Test message 2 - Hello Kafka 4.0 KRaft!"
        "Test message 3 - Cluster test successful"
        "Test message 4 - SASL authentication working"
        "Test message 5 - Final test message"
    )
    
    # Send messages
    for i in "${!test_messages[@]}"; do
        echo "${test_messages[$i]}" | $KAFKA_HOME/bin/kafka-console-producer.sh \
            --bootstrap-server $BOOTSTRAP_SERVERS \
            --producer.config $KAFKA_HOME/config/producer.properties \
            --topic $TEST_TOPIC
        
        if [ $? -eq 0 ]; then
            print_info "Message $((i+1)) sent successfully"
        else
            print_error "Failed to send message $((i+1))"
            return 1
        fi
        
        sleep 1
    done
    
    print_info "All test messages sent successfully"
    echo ""
}

# Function to test consumer
test_consumer() {
    print_header "Testing Consumer"
    
    print_info "Consuming messages from topic: $TEST_TOPIC"
    
    # Start consumer in background and capture output
    local consumer_output=$(mktemp)
    
    timeout 10s $KAFKA_HOME/bin/kafka-console-consumer.sh \
        --bootstrap-server $BOOTSTRAP_SERVERS \
        --consumer.config $KAFKA_HOME/config/consumer.properties \
        --topic $TEST_TOPIC \
        --from-beginning > $consumer_output 2>&1 &
    
    local consumer_pid=$!
    
    # Wait for consumer to finish or timeout
    wait $consumer_pid 2>/dev/null || true
    
    # Check consumed messages
    local message_count=$(wc -l < $consumer_output)
    
    if [ $message_count -gt 0 ]; then
        print_info "Successfully consumed $message_count messages:"
        cat $consumer_output | sed 's/^/  /'
    else
        print_error "No messages consumed"
        rm -f $consumer_output
        return 1
    fi
    
    rm -f $consumer_output
    echo ""
}

# Function to test topic operations
test_topic_operations() {
    print_header "Testing Topic Operations"
    
    # List topics
    print_info "Listing all topics:"
    $KAFKA_HOME/bin/kafka-topics.sh \
        --list \
        --bootstrap-server $BOOTSTRAP_SERVERS \
        --command-config $KAFKA_HOME/config/client.properties | sed 's/^/  /'
    
    echo ""
    
    # Alter topic (add partitions)
    print_info "Adding partitions to test topic..."
    $KAFKA_HOME/bin/kafka-topics.sh \
        --alter \
        --topic $TEST_TOPIC \
        --bootstrap-server $BOOTSTRAP_SERVERS \
        --command-config $KAFKA_HOME/config/client.properties \
        --partitions 6
    
    if [ $? -eq 0 ]; then
        print_info "Partitions added successfully"
        
        # Show updated topic details
        print_info "Updated topic details:"
        $KAFKA_HOME/bin/kafka-topics.sh \
            --describe \
            --topic $TEST_TOPIC \
            --bootstrap-server $BOOTSTRAP_SERVERS \
            --command-config $KAFKA_HOME/config/client.properties
    else
        print_error "Failed to add partitions"
    fi
    
    echo ""
}

# Function to test consumer groups
test_consumer_groups() {
    print_header "Testing Consumer Groups"
    
    # Start a consumer group
    print_info "Starting consumer group test..."
    
    local group_name="test-group-$(date +%s)"
    
    # Create consumer group config
    local group_config=$(mktemp)
    cat > $group_config << EOF
bootstrap.servers=$BOOTSTRAP_SERVERS
security.protocol=SASL_PLAINTEXT
sasl.mechanism=PLAIN
sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="consumer" password="consumer-secret";
group.id=$group_name
enable.auto.commit=true
auto.commit.interval.ms=1000
session.timeout.ms=30000
auto.offset.reset=earliest
key.deserializer=org.apache.kafka.common.serialization.StringDeserializer
value.deserializer=org.apache.kafka.common.serialization.StringDeserializer
EOF
    
    # Start consumer in background
    timeout 5s $KAFKA_HOME/bin/kafka-console-consumer.sh \
        --bootstrap-server $BOOTSTRAP_SERVERS \
        --consumer.config $group_config \
        --topic $TEST_TOPIC >/dev/null 2>&1 &
    
    local consumer_pid=$!
    sleep 3
    
    # List consumer groups
    print_info "Active consumer groups:"
    $KAFKA_HOME/bin/kafka-consumer-groups.sh \
        --bootstrap-server $BOOTSTRAP_SERVERS \
        --command-config $KAFKA_HOME/config/client.properties \
        --list | sed 's/^/  /'
    
    # Describe consumer group
    print_info "Consumer group details:"
    $KAFKA_HOME/bin/kafka-consumer-groups.sh \
        --bootstrap-server $BOOTSTRAP_SERVERS \
        --command-config $KAFKA_HOME/config/client.properties \
        --describe \
        --group $group_name 2>/dev/null | sed 's/^/  /' || echo "  Group not found or no active consumers"
    
    # Clean up
    kill $consumer_pid 2>/dev/null || true
    rm -f $group_config
    
    echo ""
}

# Function to test cluster metadata
test_cluster_metadata() {
    print_header "Testing Cluster Metadata"
    
    # Get cluster metadata
    print_info "Cluster metadata:"
    $KAFKA_HOME/bin/kafka-metadata-shell.sh \
        --snapshot /export/data/kafka/__cluster_metadata-0/00000000000000000000.log 2>/dev/null | \
        head -10 | sed 's/^/  /' || echo "  Metadata shell not available or no snapshot found"
    
    # Get broker API versions
    print_info "Broker API versions:"
    $KAFKA_HOME/bin/kafka-broker-api-versions.sh \
        --bootstrap-server $BOOTSTRAP_SERVERS \
        --command-config $KAFKA_HOME/config/client.properties 2>/dev/null | \
        head -5 | sed 's/^/  /'
    
    echo ""
}

# Function to cleanup test resources
cleanup_test_resources() {
    print_header "Cleaning Up Test Resources"
    
    print_info "Deleting test topic: $TEST_TOPIC"
    
    $KAFKA_HOME/bin/kafka-topics.sh \
        --delete \
        --topic $TEST_TOPIC \
        --bootstrap-server $BOOTSTRAP_SERVERS \
        --command-config $KAFKA_HOME/config/client.properties
    
    if [ $? -eq 0 ]; then
        print_info "Test topic deleted successfully"
    else
        print_warn "Failed to delete test topic (it may not exist)"
    fi
    
    echo ""
}

# Function to run performance test
run_performance_test() {
    print_header "Performance Test"
    
    local perf_topic="perf-test-$(date +%s)"
    
    print_info "Creating performance test topic: $perf_topic"
    
    # Create performance test topic
    $KAFKA_HOME/bin/kafka-topics.sh \
        --create \
        --topic $perf_topic \
        --bootstrap-server $BOOTSTRAP_SERVERS \
        --command-config $KAFKA_HOME/config/client.properties \
        --partitions 6 \
        --replication-factor 3
    
    # Producer performance test
    print_info "Running producer performance test..."
    $KAFKA_HOME/bin/kafka-producer-perf-test.sh \
        --topic $perf_topic \
        --num-records 1000 \
        --record-size 1024 \
        --throughput 100 \
        --producer.config $KAFKA_HOME/config/producer.properties
    
    # Consumer performance test
    print_info "Running consumer performance test..."
    timeout 10s $KAFKA_HOME/bin/kafka-consumer-perf-test.sh \
        --topic $perf_topic \
        --bootstrap-server $BOOTSTRAP_SERVERS \
        --consumer.config $KAFKA_HOME/config/consumer.properties \
        --messages 1000 || true
    
    # Clean up performance test topic
    print_info "Cleaning up performance test topic..."
    $KAFKA_HOME/bin/kafka-topics.sh \
        --delete \
        --topic $perf_topic \
        --bootstrap-server $BOOTSTRAP_SERVERS \
        --command-config $KAFKA_HOME/config/client.properties
    
    echo ""
}

# Main function
main() {
    local test_type=${1:-"basic"}
    
    print_info "Kafka 4.0 KRaft Cluster Test"
    print_info "Test type: $test_type"
    echo ""
    
    # Check if cluster is accessible
    if ! $KAFKA_HOME/bin/kafka-broker-api-versions.sh \
        --bootstrap-server $BOOTSTRAP_SERVERS \
        --command-config $KAFKA_HOME/config/client.properties \
        >/dev/null 2>&1; then
        print_error "Cluster is not accessible. Please check if Kafka is running."
        exit 1
    fi
    
    case $test_type in
        "basic"|"all")
            create_test_topic
            test_producer
            test_consumer
            test_topic_operations
            cleanup_test_resources
            ;;& # Continue to next case
        "groups")
            if [ "$test_type" = "groups" ] || [ "$test_type" = "all" ]; then
                create_test_topic
                test_consumer_groups
                cleanup_test_resources
            fi
            ;;& # Continue to next case
        "metadata")
            if [ "$test_type" = "metadata" ] || [ "$test_type" = "all" ]; then
                test_cluster_metadata
            fi
            ;;& # Continue to next case
        "performance")
            if [ "$test_type" = "performance" ] || [ "$test_type" = "all" ]; then
                run_performance_test
            fi
            ;;
        *)
            print_error "Unknown test type: $test_type"
            echo "Usage: $0 [basic|groups|metadata|performance|all]"
            exit 1
            ;;
    esac
    
    print_info "🎉 All tests completed successfully!"
}

# Show usage if help requested
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    echo "Usage: $0 [test_type]"
    echo ""
    echo "Test types:"
    echo "  basic       - Basic producer/consumer test (default)"
    echo "  groups      - Consumer groups test"
    echo "  metadata    - Cluster metadata test"
    echo "  performance - Performance test"
    echo "  all         - Run all tests"
    echo ""
    exit 0
fi

# Run main function
main "$@"
