#!/bin/bash

# Kafka 4.0 KRaft Cluster Status Check Script
# This script checks the status and health of the Kafka cluster

set -e

# Configuration
KAFKA_HOME="/export/server/kafka"
LOG_DIR="/export/logs/kafka"
SERVERS=("***************" "***************" "***************")

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Function to check individual node status
check_node_status() {
    local server=$1
    local node_id=$2
    
    print_header "Node $node_id Status ($server)"
    
    # Check if server is reachable
    if ! ssh -o ConnectTimeout=5 root@$server exit 2>/dev/null; then
        print_error "Cannot connect to $server"
        return 1
    fi
    
    # Check Kafka process
    local kafka_processes=$(ssh root@$server "ps aux | grep kafka.Kafka | grep -v grep")
    if [ -n "$kafka_processes" ]; then
        echo "✓ Kafka Process: Running"
        echo "  PID: $(echo "$kafka_processes" | awk '{print $2}')"
        echo "  CPU: $(echo "$kafka_processes" | awk '{print $3}')%"
        echo "  Memory: $(echo "$kafka_processes" | awk '{print $4}')%"
    else
        echo "✗ Kafka Process: Not running"
    fi
    
    # Check ports
    local port_9092=$(ssh root@$server "netstat -tlnp | grep :9092")
    local port_9093=$(ssh root@$server "netstat -tlnp | grep :9093")
    local port_9999=$(ssh root@$server "netstat -tlnp | grep :9999")
    
    if [ -n "$port_9092" ]; then
        echo "✓ Broker Port 9092: Listening"
    else
        echo "✗ Broker Port 9092: Not listening"
    fi
    
    if [ -n "$port_9093" ]; then
        echo "✓ Controller Port 9093: Listening"
    else
        echo "✗ Controller Port 9093: Not listening"
    fi
    
    if [ -n "$port_9999" ]; then
        echo "✓ JMX Port 9999: Listening"
    else
        echo "✗ JMX Port 9999: Not listening"
    fi
    
    # Check disk usage
    local data_disk=$(ssh root@$server "df -h /export/data/kafka 2>/dev/null | tail -1")
    local log_disk=$(ssh root@$server "df -h /export/logs/kafka 2>/dev/null | tail -1")
    
    if [ -n "$data_disk" ]; then
        echo "📁 Data Directory: $(echo "$data_disk" | awk '{print $5}') used"
    fi
    
    if [ -n "$log_disk" ]; then
        echo "📁 Log Directory: $(echo "$log_disk" | awk '{print $5}') used"
    fi
    
    # Check recent log entries
    echo "📋 Recent Log Entries:"
    ssh root@$server "tail -3 $LOG_DIR/server.log 2>/dev/null | sed 's/^/  /' || echo '  No recent logs found'"
    
    echo ""
}

# Function to check cluster-wide status
check_cluster_status() {
    print_header "Cluster Status"
    
    # Check if cluster is accessible
    if ! $KAFKA_HOME/bin/kafka-broker-api-versions.sh \
        --bootstrap-server ***************:9092 \
        --command-config $KAFKA_HOME/config/client.properties \
        >/dev/null 2>&1; then
        print_error "Cluster is not accessible"
        return 1
    fi
    
    print_info "Cluster is accessible"
    
    # Get broker list
    echo "🖥️  Active Brokers:"
    $KAFKA_HOME/bin/kafka-broker-api-versions.sh \
        --bootstrap-server ***************:9092 \
        --command-config $KAFKA_HOME/config/client.properties 2>/dev/null | \
        grep "^[0-9]" | sed 's/^/  Broker /'
    
    # Get topic list
    echo ""
    echo "📝 Topics:"
    local topics=$($KAFKA_HOME/bin/kafka-topics.sh \
        --bootstrap-server ***************:9092 \
        --command-config $KAFKA_HOME/config/client.properties \
        --list 2>/dev/null)
    
    if [ -n "$topics" ]; then
        echo "$topics" | sed 's/^/  /'
        
        # Show topic details for first few topics
        echo ""
        echo "📊 Topic Details (first 3 topics):"
        echo "$topics" | head -3 | while read topic; do
            if [ -n "$topic" ]; then
                echo "  Topic: $topic"
                $KAFKA_HOME/bin/kafka-topics.sh \
                    --bootstrap-server ***************:9092 \
                    --command-config $KAFKA_HOME/config/client.properties \
                    --describe --topic "$topic" 2>/dev/null | \
                    grep -E "(Partition|Leader|Replicas)" | sed 's/^/    /'
            fi
        done
    else
        echo "  No topics found"
    fi
    
    echo ""
}

# Function to check cluster health
check_cluster_health() {
    print_header "Cluster Health Check"
    
    local healthy=true
    
    # Check if all expected brokers are online
    local expected_brokers=3
    local online_brokers=$($KAFKA_HOME/bin/kafka-broker-api-versions.sh \
        --bootstrap-server ***************:9092 \
        --command-config $KAFKA_HOME/config/client.properties 2>/dev/null | \
        grep "^[0-9]" | wc -l)
    
    if [ "$online_brokers" -eq "$expected_brokers" ]; then
        echo "✓ All $expected_brokers brokers are online"
    else
        echo "✗ Only $online_brokers out of $expected_brokers brokers are online"
        healthy=false
    fi
    
    # Check for under-replicated partitions
    echo "🔍 Checking for under-replicated partitions..."
    local under_replicated=$($KAFKA_HOME/bin/kafka-topics.sh \
        --bootstrap-server ***************:9092 \
        --command-config $KAFKA_HOME/config/client.properties \
        --describe 2>/dev/null | grep "UnderReplicated" | wc -l)
    
    if [ "$under_replicated" -eq 0 ]; then
        echo "✓ No under-replicated partitions found"
    else
        echo "✗ Found $under_replicated under-replicated partitions"
        healthy=false
    fi
    
    # Overall health status
    echo ""
    if $healthy; then
        print_info "🎉 Cluster is healthy!"
    else
        print_warn "⚠️  Cluster has some issues that need attention"
    fi
    
    echo ""
}

# Function to show performance metrics
show_performance_metrics() {
    print_header "Performance Metrics"
    
    for i in "${!SERVERS[@]}"; do
        server=${SERVERS[$i]}
        node_id=$((i + 1))
        
        echo "Node $node_id ($server):"
        
        # CPU and Memory usage
        local system_info=$(ssh root@$server "top -bn1 | grep 'Cpu\|Mem'")
        echo "$system_info" | sed 's/^/  /'
        
        # Network connections
        local connections=$(ssh root@$server "netstat -an | grep :9092 | wc -l")
        echo "  Active connections to port 9092: $connections"
        
        echo ""
    done
}

# Function to show recent errors
show_recent_errors() {
    print_header "Recent Errors"
    
    for i in "${!SERVERS[@]}"; do
        server=${SERVERS[$i]}
        node_id=$((i + 1))
        
        echo "Node $node_id ($server):"
        
        # Check for errors in server log
        local errors=$(ssh root@$server "grep -i 'error\|exception\|failed' $LOG_DIR/server.log 2>/dev/null | tail -3")
        
        if [ -n "$errors" ]; then
            echo "$errors" | sed 's/^/  /'
        else
            echo "  No recent errors found"
        fi
        
        echo ""
    done
}

# Main function
main() {
    local check_type=${1:-"all"}
    
    print_info "Kafka 4.0 KRaft Cluster Status Check"
    print_info "Check type: $check_type"
    echo ""
    
    case $check_type in
        "nodes"|"all")
            # Check individual node status
            for i in "${!SERVERS[@]}"; do
                server=${SERVERS[$i]}
                node_id=$((i + 1))
                check_node_status $server $node_id
            done
            ;;& # Continue to next case
        "cluster"|"all")
            # Check cluster status
            check_cluster_status
            ;;& # Continue to next case
        "health"|"all")
            # Check cluster health
            check_cluster_health
            ;;& # Continue to next case
        "performance")
            if [ "$check_type" = "performance" ] || [ "$check_type" = "all" ]; then
                show_performance_metrics
            fi
            ;;& # Continue to next case
        "errors")
            if [ "$check_type" = "errors" ] || [ "$check_type" = "all" ]; then
                show_recent_errors
            fi
            ;;
        *)
            print_error "Unknown check type: $check_type"
            echo "Usage: $0 [nodes|cluster|health|performance|errors|all]"
            exit 1
            ;;
    esac
    
    print_info "Status check completed!"
}

# Show usage if help requested
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    echo "Usage: $0 [check_type]"
    echo ""
    echo "Check types:"
    echo "  nodes       - Check individual node status"
    echo "  cluster     - Check cluster-wide status"
    echo "  health      - Check cluster health"
    echo "  performance - Show performance metrics"
    echo "  errors      - Show recent errors"
    echo "  all         - Run all checks (default)"
    echo ""
    exit 0
fi

# Run main function
main "$@"
