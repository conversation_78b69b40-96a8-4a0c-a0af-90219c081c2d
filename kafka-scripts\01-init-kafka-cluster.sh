#!/bin/bash

# Kafka 4.0 KRaft Cluster Initialization Script
# This script initializes the Kafka cluster in KRaft mode

set -e

# Configuration
KAFKA_HOME="/export/server/kafka"
DATA_DIR="/export/data/kafka"
LOG_DIR="/export/logs/kafka"
SERVERS=("***************" "***************" "***************")
CLUSTER_ID=""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if SSH connection is available
check_ssh_connection() {
    local server=$1
    if ssh -o ConnectTimeout=5 -o BatchMode=yes root@$server exit 2>/dev/null; then
        return 0
    else
        return 1
    fi
}

# Function to create directories on remote servers
create_directories() {
    local server=$1
    print_info "Creating directories on $server..."
    
    ssh root@$server "
        mkdir -p $DATA_DIR
        mkdir -p $LOG_DIR
        mkdir -p $KAFKA_HOME/config
        chown -R kafka:kafka $DATA_DIR
        chown -R kafka:kafka $LOG_DIR
        chown -R kafka:kafka $KAFKA_HOME
    "
}

# Function to generate cluster ID
generate_cluster_id() {
    print_info "Generating cluster ID..."
    CLUSTER_ID=$($KAFKA_HOME/bin/kafka-storage.sh random-uuid)
    print_info "Generated cluster ID: $CLUSTER_ID"
}

# Function to format storage on each node
format_storage() {
    local server=$1
    local node_id=$2
    
    print_info "Formatting storage on $server (node-$node_id)..."
    
    ssh root@$server "
        export KAFKA_OPTS='-Djava.security.auth.login.config=$KAFKA_HOME/config/kafka_server_jaas.conf'
        cd $KAFKA_HOME
        bin/kafka-storage.sh format -t $CLUSTER_ID -c config/server.properties
    "
}

# Function to deploy configuration files
deploy_config() {
    local server=$1
    local node_id=$2
    
    print_info "Deploying configuration files to $server..."
    
    # Copy server.properties
    scp kafka-config/node-$node_id/server.properties root@$server:$KAFKA_HOME/config/
    
    # Copy common configuration files
    scp kafka-config/common/log4j.properties root@$server:$KAFKA_HOME/config/
    scp kafka-config/common/kafka_server_jaas.conf root@$server:$KAFKA_HOME/config/
    scp kafka-config/common/client.properties root@$server:$KAFKA_HOME/config/
    scp kafka-config/common/producer.properties root@$server:$KAFKA_HOME/config/
    scp kafka-config/common/consumer.properties root@$server:$KAFKA_HOME/config/
    
    # Set permissions
    ssh root@$server "chown -R kafka:kafka $KAFKA_HOME/config/"
}

# Main initialization process
main() {
    print_info "Starting Kafka 4.0 KRaft cluster initialization..."
    
    # Check if Kafka is installed
    if [ ! -d "$KAFKA_HOME" ]; then
        print_error "Kafka not found at $KAFKA_HOME. Please install Kafka first."
        exit 1
    fi
    
    # Check SSH connections to all servers
    print_info "Checking SSH connections..."
    for i in "${!SERVERS[@]}"; do
        server=${SERVERS[$i]}
        if ! check_ssh_connection $server; then
            print_error "Cannot connect to $server via SSH"
            exit 1
        fi
        print_info "SSH connection to $server: OK"
    done
    
    # Generate cluster ID
    generate_cluster_id
    
    # Initialize each node
    for i in "${!SERVERS[@]}"; do
        server=${SERVERS[$i]}
        node_id=$((i + 1))
        
        print_info "Initializing node $node_id on $server..."
        
        # Create directories
        create_directories $server
        
        # Deploy configuration files
        deploy_config $server $node_id
        
        # Format storage
        format_storage $server $node_id
        
        print_info "Node $node_id on $server initialized successfully"
    done
    
    print_info "Kafka 4.0 KRaft cluster initialization completed!"
    print_info "Cluster ID: $CLUSTER_ID"
    print_warn "Please save the cluster ID for future reference."
    print_info "You can now start the cluster using: ./02-start-kafka-cluster.sh"
}

# Check if running as root or with sudo
if [ "$EUID" -ne 0 ]; then
    print_error "This script must be run as root or with sudo"
    exit 1
fi

# Run main function
main "$@"
