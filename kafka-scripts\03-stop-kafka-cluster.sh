#!/bin/bash

# Kafka 4.0 KRaft Cluster Stop Script
# This script stops the Kafka cluster gracefully

set -e

# Configuration
KAFKA_HOME="/export/server/kafka"
LOG_DIR="/export/logs/kafka"
SERVERS=("***************" "***************" "***************")

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Kafka is running
check_kafka_running() {
    local server=$1
    local result=$(ssh root@$server "ps aux | grep kafka | grep -v grep | wc -l")
    return $result
}

# Function to stop Kafka gracefully
stop_kafka_graceful() {
    local server=$1
    local node_id=$2
    
    print_info "Stopping Kafka gracefully on $server (node-$node_id)..."
    
    # Check if running
    if ! check_kafka_running $server; then
        print_warn "Kafka is not running on $server"
        return 0
    fi
    
    # Try graceful shutdown first
    ssh root@$server "
        cd $KAFKA_HOME
        bin/kafka-server-stop.sh
    " 2>/dev/null || true
    
    # Wait for graceful shutdown
    local attempts=0
    local max_attempts=30
    
    while [ $attempts -lt $max_attempts ]; do
        if ! check_kafka_running $server; then
            print_info "Kafka stopped gracefully on $server"
            return 0
        fi
        sleep 2
        ((attempts++))
    done
    
    print_warn "Graceful shutdown timed out on $server, trying force stop..."
    return 1
}

# Function to force stop Kafka
stop_kafka_force() {
    local server=$1
    local node_id=$2
    
    print_warn "Force stopping Kafka on $server (node-$node_id)..."
    
    # Kill Kafka processes
    ssh root@$server "
        # Kill by PID file if exists
        if [ -f $LOG_DIR/kafka.pid ]; then
            kill \$(cat $LOG_DIR/kafka.pid) 2>/dev/null || true
            rm -f $LOG_DIR/kafka.pid
        fi
        
        # Kill by process name
        pkill -f kafka.Kafka 2>/dev/null || true
        
        # Wait a moment
        sleep 3
        
        # Force kill if still running
        pkill -9 -f kafka.Kafka 2>/dev/null || true
    "
    
    # Verify stopped
    sleep 2
    if ! check_kafka_running $server; then
        print_info "Kafka force stopped on $server"
        return 0
    else
        print_error "Failed to stop Kafka on $server"
        return 1
    fi
}

# Function to stop Kafka on a server
stop_kafka_node() {
    local server=$1
    local node_id=$2
    
    # Try graceful stop first
    if stop_kafka_graceful $server $node_id; then
        return 0
    fi
    
    # If graceful stop failed, try force stop
    stop_kafka_force $server $node_id
}

# Function to clean up temporary files
cleanup_temp_files() {
    local server=$1
    
    print_info "Cleaning up temporary files on $server..."
    
    ssh root@$server "
        # Remove PID file
        rm -f $LOG_DIR/kafka.pid
        
        # Clean up any lock files
        find /export/data/kafka -name '*.lock' -delete 2>/dev/null || true
        
        # Clean up temporary directories
        find /tmp -name 'kafka-*' -type d -exec rm -rf {} + 2>/dev/null || true
    "
}

# Function to show final status
show_final_status() {
    print_info "Final cluster status:"
    
    local all_stopped=true
    
    for i in "${!SERVERS[@]}"; do
        server=${SERVERS[$i]}
        node_id=$((i + 1))
        
        echo "=== Node $node_id ($server) ==="
        
        if check_kafka_running $server; then
            echo "✗ Kafka: Still running"
            all_stopped=false
        else
            echo "✓ Kafka: Stopped"
        fi
        
        # Check ports
        local port_9092=$(ssh root@$server "netstat -tlnp | grep :9092 | wc -l")
        local port_9093=$(ssh root@$server "netstat -tlnp | grep :9093 | wc -l")
        
        if [ $port_9092 -eq 0 ]; then
            echo "✓ Port 9092: Released"
        else
            echo "✗ Port 9092: Still in use"
        fi
        
        if [ $port_9093 -eq 0 ]; then
            echo "✓ Controller port 9093: Released"
        else
            echo "✗ Controller port 9093: Still in use"
        fi
        
        echo ""
    done
    
    if $all_stopped; then
        print_info "All Kafka nodes stopped successfully!"
    else
        print_error "Some Kafka nodes are still running. Manual intervention may be required."
    fi
}

# Main function
main() {
    print_info "Stopping Kafka 4.0 KRaft cluster..."
    
    # Stop each node (reverse order for better graceful shutdown)
    for ((i=${#SERVERS[@]}-1; i>=0; i--)); do
        server=${SERVERS[$i]}
        node_id=$((i + 1))
        
        stop_kafka_node $server $node_id
        
        # Clean up temporary files
        cleanup_temp_files $server
        
        # Wait between node stops
        if [ $i -gt 0 ]; then
            sleep 5
        fi
    done
    
    # Show final status
    show_final_status
    
    print_info "Kafka 4.0 KRaft cluster shutdown completed!"
}

# Run main function
main "$@"
